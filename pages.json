{
	"easycom": {
		"u-tabs-swiper": "@/components/u-tabs-swiper/u-tabs-swiper.vue",
		// uview 组件
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue",
		// 自定义组件
		"^cl-(.*)": "@/components/cl-$1/index.vue"
	},
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/home/<USER>/index",
			"name": "pages_index",
			"style": {
				"onReachBottomDistance": 500,
				"navigationBarTitleText": "",
				// #ifndef MP-TOUTIAO || MP-ALIPAY
				"navigationStyle": "custom", // 隐藏系统导航栏
				// #endif
				"app-plus": {
					"transparentTitle": "auto"
				}
			}
		},
		{
			"path": "pages/home/<USER>/userinfo",
			"name": "user_userinfo",
			"meta": {
				"user": true
			},
			"style": {
				// #ifndef MP-TOUTIAO || MP-ALIPAY
				"navigationStyle": "custom", // 隐藏系统导航栏
				// #endif
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/home/<USER>/shopcate",
			"style": {
				"navigationBarTitleText": "商品列表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/home/<USER>/notice",
			"meta": {
				"user": true
			},
			"style": {
				// #ifndef MP-TOUTIAO || MP-ALIPAY
				"navigationStyle": "custom", // 隐藏系统导航栏
				// #endif
				"navigationBarTitleText": "用户通知",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/home/<USER>/shopcard",
			"name": "shop_shopcard",
			"meta": {
				"user": true
			},
			"style": {
				"navigationBarTitleText": "购物车",
				"enablePullDownRefresh": false
			}
		}
	],
	"subpackages": [
		{
			"root": "pages/made",
			"pages": [
				{
					"path": "resources_details/resources_details",
					"style": {
						// #ifndef MP-TOUTIAO || MP-ALIPAY
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "house_details/house_details",
					"style": {
						// #ifndef MP-TOUTIAO || MP-ALIPAY
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "全部功能",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "appraise/appraise",
					"style": {
						// #ifndef MP-TOUTIAO || MP-ALIPAY
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "房屋估价",
						"enablePullDownRefresh": false
					}
				},
			]
		},
		{
			"root": "pages/sub",
			"pages": [
				{
					"path": "login/login",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "close/close",
					"style": {
						"navigationStyle": "custom", // 取消本页面的导航栏
						"app-plus": {
							"animationType": "fade-in", // 设置fade-in淡入动画，为最合理的动画类型
							"background": "transparent", // 背景透明
							"backgroundColor": "rgba(0,0,0,0)", // 背景透明
							"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
							"titleNView": { //  隐藏当前页面的返回按钮
								"titleSize": "18px",
								"autoBackButton": false // 禁用原生导航栏
							}
						}
					}
				},
				{
					"path": "text/text",
					"meta": {
						"user": true
					}
				},
				{
					"path": "text/live",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "webview/webview",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "material/material",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#feeade",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "素材库",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "material/add_material",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发布素材",
						"enablePullDownRefresh": false
					}
				},
				// {
				// 	"path": "tutor/tutor",
				// 	"meta": {
				// 		"user": true
				// 	},
				// 	"style": {
				// 		"navigationBarTitleText": "运营策划",
				// 		"enablePullDownRefresh": false
				// 	}
				// },
				{
					"path": "form/form",
					"style": {
						"navigationBarTitleText": "表单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "form/formlist",
					"style": {
						"navigationBarTitleText": "记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "signin/signin",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#ec5c35",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "签到",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "rider_center/rider_center",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "骑手中心",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_center",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "接单中心",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_detail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "服务详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_ranking",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "排行榜",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_statistics",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "订单统计",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_masterlist",
					"meta": {
						"user": false
					},
					"style": {
						"navigationBarTitleText": "找师傅",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_masterdetail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "师傅详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_cancel",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "取消原因",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_mastereditinfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "修改师傅信息",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "theme/theme",
					"style": {
						"navigationBarTitleText": "",
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_permanent",
					"style": {
						"navigationBarTitleText": "常驻地址/服务时间",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "circle/circle",
					"style": {
						"navigationBarTitleText": "随手拍",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "circle/circle_add",
					"style": {
						"navigationBarTitleText": "随手拍",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "college/college_list",
					"style": {
						"navigationBarTitleText": "商学院",
						"navigationBarTextStyle": "white",
						"navigationBarBackgroundColor": "#278BEB",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "college/college_detail",
					"style": {
						"navigationBarBackgroundColor": "#278BEB",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "商学院",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "college/college_exam",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#278BEB",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "我要考试",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "college/college_score",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#278BEB",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "我的成绩",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "college/college_record",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#278BEB",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "考试记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "platoon/info",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#2e2e30",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "我的点位",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "platoon/detail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#2e2e30",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "点位层级",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "life/center",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#0d82df",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "充值中心",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "life/record",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#0d82df",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "充值缴费账单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "store/store_page",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "store/store_manage",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "管理商品",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "store/store_grounding",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "上架商品",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "store/store_success",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "上架成功",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "store/store_setting",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "微店设置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiving/receiving_apply",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "技师入驻",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity/activity_list",
					"meta": {
						"user": false
					},
					"style": {
						"navigationBarTitleText": "线下活动",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity/activity_detail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "活动详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity/activity_form",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "活动报名",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mention/mention_apply",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "申请门店",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mention/mention_edit",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "信息修改",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mention/mention_writeoff",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "自提核销",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mention/mention_choose",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				//------------云仓------------
				{
					"path": "yc/yc_mall",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "我要进货",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yc/yc_order",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "云仓订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yc/yc_team",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的团队",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yc/yc_myproduct",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的云仓",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yc/yc_outlet",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "零售出仓",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yc/yc_exchagegoods",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "零售出仓",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yc/yc_authorization",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "资质证书",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yc/yc_market",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "市场数据",
						"enablePullDownRefresh": false
					}
				},
				//------------跑腿------------
				{
					"path": "errand/errand_index",
					"meta": {
						"user": false
					},
					"style": {
						"navigationBarTitleText": "跑腿",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand/errand_buy",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "帮我买",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand/errand_confirm",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "确认订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand/errand_orderlist",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand/errand_cancel",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "取消订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand/errand_editphone",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "修改发件人手机号码",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand/errand_editnotes",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "备注",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand/errand_address",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "取货地址",
						"enablePullDownRefresh": false
					}
				},
				//------------分类信息------------
				{
					"path": "cityinfo/index",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/add",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发布信息",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/form",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发布信息",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/myinfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的信息",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/myreserve",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的预约",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/myreply",
					"style": {
						"navigationBarTitleText": "我的评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/detail",
					"style": {
						"navigationBarTitleText": "信息详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/comment",
					"style": {
						"navigationBarTitleText": "更多评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cityinfo/list",
					"style": {
						"navigationBarTitleText": "分类信息",
						"enablePullDownRefresh": false
					}
				},
				//app上架
				{
					"path": "grounding/help",
					"meta": {
						"user": false
					},
					"style": {
						"navigationBarTitleText": "帮助与设置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "grounding/grxx",
					"meta": {
						"user": false
					},
					"style": {
						"navigationBarTitleText": "个人信息管理",
						"enablePullDownRefresh": false
					}
				},
				//------------圈子动态--------
				{
					"path": "trends/trends_index",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_list",
					"style": {
						"navigationBarTitleText": "圈子动态",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_my",
					"style": {
						"navigationBarTitleText": "我发布的",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_add",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_add2",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_comment",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_detail",
					"style": {
						"navigationBarTitleText": "动态详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_follow",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_personal",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "trends/trends_report",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				//------------九宫格动态--------
				{
					"path": "lottery/nine_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "九宫格抽奖",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "lottery/nine_lootery",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "lottery/nine_record",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "抽奖记录",
						"enablePullDownRefresh": false
					}
				},
				//------------外卖------------
				{
					"path": "chain/chain",
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "chain/chain_maps",
					"style": {
						// #ifndef MP-TOUTIAO || MP-ALIPAY
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "chain/chain_search",
					"style": {
						"navigationBarTitleText": "搜索门店",
						"enablePullDownRefresh": false
					}
				},
				// {
				// 	"path": "chain/chain_home",
				// 	"style": {
				// 		// #ifndef MP-TOUTIAO || MP-ALIPAY
				// 		"navigationStyle": "custom", // 隐藏系统导航栏
				// 		// #endif
				// 		"navigationBarTitleText": "",
				// 		"enablePullDownRefresh": false
				// 	}
				// },
				//------------闲置------------
				{
					"path": "market/index",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "闲置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "market/issue_market",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发闲置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "market/market_detail",
					"style": {
						"navigationBarTitleText": "闲置详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "market/market_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "market/market_delivery",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				//CRM
				{
					"path": "crm/client_public",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "公海客户",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "crm/client_my",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "crm/client_transaction",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的成交",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "crm/footprint",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "浏览记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "crm/follow_info",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/user",
			"pages": [
				{
					"path": "shopcard/shopcard2",
					"name": "shop_shopcard2",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "购物车",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "share/share",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "分享",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "coupon/coupon",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "优惠券",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "footprint/footprint",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "足迹",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collect/collect",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的收藏",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "team/team",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#f9e6ce",
						"navigationBarTitleText": "我的团队",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "bindPhone/bindPhone",
					"style": {
						"navigationBarTitleText": "修改手机号",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "feedbacklist/feedbacklist",
					"style": {
						"navigationBarTitleText": "我的反馈",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "feedback/feedback",
					"style": {
						"navigationBarTitleText": "意见反馈",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "membercode/membercode",
					"style": {
						"navigationBarTitleText": "会员码",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "notice_detail/notice_detail",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "apply_agent/apply_agent",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "agent_center/agent_center",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "apply_rider/apply_rider",
					"style": {
						"navigationBarTitleText": "申请骑手",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "un_enable/un_enable",
					"style": {
						"navigationBarTitleText": "未激活",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "bindShare/bindShare",
					"style": {
						"navigationBarTitleText": "绑定推荐人",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/chat",
			"pages": [
				{
					"path": "chat_page/chat_page",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#ffffff",
						"backgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"disableScroll": true,
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "chat_page/send_thing",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "customer_service/customer_service",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "sdk/sdk",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/index",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "聊天",
						"onReachBottomDistance": 500,
						"enablePullDownRefresh": false,
						// #ifndef MP-TOUTIAO || MP-ALIPAY
						"navigationStyle": "custom" // 隐藏系统导航栏
						// #endif
					}
				},
				{
					"path": "wechat/buddy",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "通讯录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/member",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/ewm",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "二维码",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/newfriend",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "新的朋友",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/search",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "搜索",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/usercard",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "详情资料",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/userdetail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "详细资料",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/setremark",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "设置备注",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/commongroup",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "共同群聊",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/groupcard",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/addfriend",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "添加朋友",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/choosefriend",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "选择好友",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/group",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "群聊",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/chatinfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "聊天信息",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/groupmember",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/chat",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/sendred",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#DA4143",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "发红包",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/transfer",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "转账",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/redinfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#DA4143",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "红包详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/transferinfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "转账详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/payment",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "收付款",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/paymentset",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "设置金额",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "wechat/transaction",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "付款",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "circle/circle",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "动态",
						"enablePullDownRefresh": false,
						// #ifndef MP-TOUTIAO || MP-ALIPAY
						"navigationStyle": "custom" // 隐藏系统导航栏
						// #endif
					}
				},
				{
					"path": "circle/permissions",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "朋友权限",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "circle/form",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发布朋友圈",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "circle/comment",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的动态",
						"enablePullDownRefresh": false
					}
				},
				// 群发助手
				{
					"path": "message_assistant/index",
					"name": "message_assistant_index",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "群发助手",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "message_assistant/new_send",
					"name": "message_assistant_new_send",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "群发助手",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "message_assistant/compose_message",
					"name": "message_assistant_compose_message",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EFEFEF",
						"navigationBarTextStyle": "black",
						"navigationBarTitleText": "群发",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/shop",
			"pages": [
				{
					"path": "shopdetail/shopdetail",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shoplist/shoplist",
					"name": "shop_shoplist",
					"style": {
						"navigationBarTitleText": "商品列表" // 隐藏系统导航栏
					}
				},
				{
					"path": "shopcate/shopcate",
					"style": {
						"navigationBarTitleText": "商品列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_groupbuy/activity_groupbuy",
					"style": {
						"navigationBarTitleText": "拼购专区",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_groupbuy/activity_groupbuy_record",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "拼购记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_cut/activity_cut",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "疯狂砍价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_cut_detail/activity_cut_detail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "帮我砍价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_grouping/activity_grouping",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "拼团",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_grouping/activity_grouping_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "超值拼团",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_list/activity_list",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shopsearch/shopsearch",
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "搜索",
						"backgroundColorTop": "#F4F4F4",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shopevaluate/shopevaluate",
					"style": {
						"navigationBarTitleText": "商品评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "supplier/supplier",
					"style": {
						"navigationBarTitleText": "店铺",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shopselect/shopselect",
					"style": {
						"navigationBarTitleText": "选择商品",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shopcatelist/shopcatelist",
					"style": {
						"navigationBarTitleText": "商品分类",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_limit/activity_limit",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "限时秒杀",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity_limit1/activity_limit1",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "限时秒杀",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "member_mall/member_mall",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "会员商城",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shopservice/shopservice",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "服务内容",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "gift_card/card_list",
					"style": {
						"navigationBarBackgroundColor": "#25CF6F",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "礼品卡",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "gift_card/card_detail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#33D78E",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "礼品卡详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "gift_card/card_order_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的礼品卡"
					}
				},
				{
					"path": "gift_card/card_order_detail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "订单详情"
					}
				},
				//竞拍
				{
					"path": "auction/auction_people",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "竞拍者",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/supplier",
			"pages": [
				{
					"path": "apply/apply",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "商家入驻",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "platform/platform",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "商户工作台",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "editproduct/editproduct",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "editlist/editlist",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "editinfo/editinfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "商户信息",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receipt/receipt",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "商户收款码",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#F09640"
					}
				},
				{
					"path": "transaction/transaction",
					"style": {
						"navigationBarTitleText": "付款",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderreturn/orderreturn",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "审核退货",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receiptset/receiptset",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "商户收款码",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "returnmail/returnmail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#F7F7F7",
						"navigationBarTitleText": "退货物流",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "returndetails/returndetails",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "list/list",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/takeaway",
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "外卖商圈",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/takeaway_shop",
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "delivery/delivery",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发货",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop_cate_tree/shop_cate_tree",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "分类管理",
						"enablePullDownRefresh": false
					}
				},
				//------------微展业------------
				{
					"path": "drag/drag_user",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "微展业",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "drag/drag_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "展业列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "drag/customer_rank",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "排行",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "drag/customer_info",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "drag/customer_follow",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "用户跟进",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "drag/customer_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "客户列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "drag/follow_record",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "跟进记录",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/pay",
			"pages": [
				{
					"path": "recharge/recharge",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "充值",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "withdraw/withdraw",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "提现",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cashflow/cashflow",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarBackgroundColor": "#EB604D",
						"navigationBarTextStyle": "white",
						"navigationBarTitleText": "资金流水",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "cashflow/cashflow_details",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "账单详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "payment/payment",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "收银台",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "paystatus/paystatus",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "订单支付",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record/record",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mychange/mychange",
					"style": {
						"navigationBarBackgroundColor": "#ECE1CC",
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "fundtransfer/fundtransfer",
					"style": {
						"navigationBarTitleText": "资金互转",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "transferrecord/transferrecord",
					"style": {
						"navigationBarTitleText": "互转记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "fundexchange/fundexchange",
					"style": {
						"navigationBarTitleText": "资金兑换",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "exchangerecord/exchangerecord",
					"style": {
						"navigationBarTitleText": "兑换记录",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/news",
			"pages": [
				{
					"path": "protocol/protocol",
					"style": {
						"navigationBarTitleText": "协议",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "newsdetail/newsdetail",
					"style": {
						"navigationBarTitleText": "详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "newslist/newslist",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "newsbigs/newsbigs",
					"style": {
						"navigationBarTitleText": "资讯",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "newsinfo/newsinfo",
					"style": {
						"navigationBarTitleText": "资讯",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/setting",
			"pages": [
				{
					"path": "userinfo/userinfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的资料",
						"enablePullDownRefresh": false,
						"backgroundColor": "#f3f3f3"
					}
				},
				{
					"path": "index/index",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "设置",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "addresslist/addresslist",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "收货地址",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "addressform/addressform",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "setwechat/setwechat",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "关联微信",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "setalipay/setalipay",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "关联支付宝",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "realname/realname",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "实名认证",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "loginpassword/loginpassword",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "安全密码",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "paypassword/paypassword",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "支付密码",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "receivingaccount/receivingaccount",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "收款账号",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "appdown/appdown",
					"style": {
						"navigationBarTitleText": "APP下载",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "ratings/ratings",
					"style": {
						"navigationBarTitleText": "会员中心",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/order",
			"pages": [
				{
					"path": "orderconfirm/orderconfirm",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "订单确认页",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderlist/orderlist",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO || MP-ALIPAY
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "ordermail/ordermail",
					"style": {
						"navigationBarTitleText": "物流详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "ordermail/jh_ordermail",
					"style": {
						"navigationBarTitleText": "物流详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderReview/orderReview",
					"style": {
						"navigationBarTitleText": "评价",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderdetail/orderdetail",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderreturn/orderreturn",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "supplierorder/supplierorder",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "商户订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "riderorder/riderorder",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "骑手订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "storeorder/storeorder",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "微店订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "invoiceapply/invoiceapply",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发票服务",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "invoiceform/invoiceform",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "申请发票",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "invoicerecord/invoicerecord",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "开票历史",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "clockconfirm/clockconfirm",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "自定义加钟",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mention/mention_order",
					"style": {
						"navigationBarTitleText": "订单管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "errand_order/errand_order",
					"style": {
						"navigationBarTitleText": "跑腿订单",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderreturn/orderreturn_sup",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "申请退款",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "auctionorder/auctionorder",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "竞拍订单",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/live",
			"pages": [
				{
					"path": "reply/reply",
					"meta": {
						"user": true,
						"navigationBarTitleText": "短视频页面"
					}
				},
				{
					"path": "live_push/live_push",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "开始直播"
					}
				},
				{
					"path": "live_obs/live_obs",
					"style": {
						"navigationBarTitleText": "obs直播",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "live_pull/live_pull",
					"meta": {
						"user": true
					},
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom", // 隐藏系统导航栏
						// #endif
						"navigationBarTitleText": "观看直播",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "apply/apply",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "申请主播"
					}
				},
				{
					"path": "release_form/release_form",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发布短视频",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "follow_list/follow_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的关注",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "home_page/home_page",
					"style": {
						"navigationBarTitleText": "主页",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "comment_list/comment_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的评论",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/lesson",
			"pages": [
				{
					"path": "lesson/lesson",
					"style": {
						// #ifndef MP-TOUTIAO
						"navigationStyle": "custom",
						// #endif
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "lessonsearch/lessonsearch",
					"style": {
						"navigationBarTitleText": "搜索",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "lessoncate/lessoncate",
					"style": {
						"navigationBarTitleText": "分类",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "lessoninfo/lessoninfo",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "详情",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/shareholder", //V2情况下 解开注释
			"pages": [
				{
					"path": "staff/staff",
					"style": {
						"navigationBarTitleText": "工作台",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "saveorder/saveorder",
					"style": {
						"navigationBarTitleText": "订单支付",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "addshop/addshop",
					"style": {
						"navigationBarTitleText": "全部商品",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "replenish/replenish",
					"style": {
						"navigationBarTitleText": "库存管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "orderlist/orderlist",
					"style": {
						"navigationBarTitleText": "订单管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mywallet/mywallet",
					"style": {
						"navigationBarTitleText": "我的钱包",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "marketing/marketing",
					"style": {
						"navigationBarTitleText": "营销工具",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "intellectcustom/intellectcustom",
					"style": {
						"navigationBarTitleText": "智能抓客",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "mycustom/mycustom",
					"style": {
						"navigationBarTitleText": "我的客户",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shareholder/shareholder",
					"style": {
						"navigationBarTitleText": "工作台",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "apply/apply",
					"style": {
						"navigationBarTitleText": "股东申请",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "equitycoupon/equitycoupon",
					"style": {
						"navigationBarTitleText": "优惠券",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "myguest/myguest",
					"style": {
						"navigationBarTitleText": "社区",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "reportform/reportform",
					"style": {
						"navigationBarTitleText": "实收报表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "custominfo/custominfo",
					"style": {
						"navigationBarTitleText": "个人中心",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "walletdetails/walletdetails",
					"style": {
						"navigationBarTitleText": "钱包明细",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "addcustom/addcustom",
					"style": {
						"navigationBarTitleText": "添加客户",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "replenishlists/replenishlists",
					"style": {
						"navigationBarTitleText": "补货申请",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "marketingcircle/marketingcircle",
					"style": {
						"navigationBarTitleText": "营销素材圈",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "staffmanage/staffmanage",
					"style": {
						"navigationBarTitleText": "员工管理",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "sourceadd/sourceadd",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "personnel/personnel",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "personnelinfo/personnelinfo",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "businesscard/businesscard",
					"style": {
						"navigationBarTitleText": "名片",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/oa",
			"pages": [
				{
					"path": "clock/clock",
					"style": {
						"navigationBarTitleText": "考勤打卡",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "clockrecord/clockrecord",
					"style": {
						"navigationBarTitleText": "打卡记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "approve/approve",
					"style": {
						"navigationBarTitleText": "考勤申请",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "daily/daily",
					"style": {
						"navigationBarTitleText": "提交日报",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "reimburse/reimburse",
					"style": {
						"navigationBarTitleText": "报销申请",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record/record",
					"style": {
						"navigationBarTitleText": "跟进记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "contract/contract",
					"style": {
						"navigationBarTitleText": "电子合同",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "contractsave/contractsave",
					"style": {
						"navigationBarTitleText": "添加合同",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "contractinfo/contractinfo",
					"style": {
						"navigationBarTitleText": "合同详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "contractlist/contractlist",
					"style": {
						"navigationBarTitleText": "合同列表",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "approvelists/approvelists",
					"style": {
						"navigationBarTitleText": "申请记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "dailylists/dailylists",
					"style": {
						"navigationBarTitleText": "日报记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "reimburselists/reimburselists",
					"style": {
						"navigationBarTitleText": "报销记录",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/job",
			"pages": [
				{
					"path": "index/index",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "post/post",
					"meta": {
						"user": true,
						"type": 1
					},
					"style": {
						"navigationBarTitleText": "求职",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "recruit_list/recruit_list",
					"meta": {
						"user": true,
						"type": 2
					},
					"style": {
						"navigationBarTitleText": "招人",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "seek_job/seek_job",
					"meta": {
						// "user": true,
						"type": 1
					},
					"style": {
						"navigationBarTitleText": "找工作",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "job_detail/job_detail",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "company_detail/company_detail",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "job_fairs/job_fairs",
					"style": {
						"navigationBarTitleText": "招聘会",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "fairs_detail/fairs_detail",
					"style": {
						"navigationBarTitleText": "招聘会详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "fairs_company/fairs_company",
					"style": {
						"navigationBarTitleText": "招聘企业",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "company_apply/company_apply",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "企业报名",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "skill_list/skill_list",
					"style": {
						"navigationBarTitleText": "技能培训",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "skill_enroll/skill_enroll",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "已报名培训",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "interview_list/interview_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "面试邀请",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my_resume/my_resume",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的在线简历",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "resume_detail/resume_detail",
					"style": {
						"navigationBarTitleText": "简历详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "search_page/search_page",
					// "meta": {
					// 	"user": true
					// },
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "company_approve/company_approve",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "企业认证",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "company_edit/company_edit",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "完善企业资料",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "post_list/post_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "岗位编辑",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "post_form/post_form",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "interested_list/interested_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "对我感兴趣的公司",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collect_list/collect_list",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "我的收藏",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "interview_send/interview_send",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发起面试邀请",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "resume_send/resume_send",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "发送简历",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "resume_mutual/resume_mutual",
					"meta": {
						"user": true
					},
					"style": {
						"navigationBarTitleText": "", //简历列表
						"enablePullDownRefresh": false
					}
				}
			]
		}
	],
	"dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize",
	"globalStyle": {
		// #ifdef H5 || APP-PLUS
		"navigationStyle": "custom",
		// #endif
		// #ifdef MP-TOUTIAO
		"navigationStyle": "default",
		// #endif
		"mp-weixin": {
			"titleSize": "38"
		},
		// "navigationStyle": "custom",
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#f3f3f3",
		"backgroundColorTop": "#f3f3f3" // iOS APP真机bounce回弹区域默认灰色,建议统一重置为白色
	},
	"tabBar": {
		// "custom": true,
		"color": "#999999",
		"selectedColor": "#18BD5E",
		"fontSize": "12px",
		"height": "50px",
		"iconWidth": "18px",
		"borderStyle": "white",
		"backgroundColor": "#FFFFFF",
		"list": [
			{
				"pagePath": "pages/home/<USER>/index",
				"iconPath": "./static/page/footer_icon1_1.png",
				"selectedIconPath": "./static/page/footer_icon1_2.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/home/<USER>/shopcate",
				"iconPath": "./static/page/footer_icon2_1.png",
				"selectedIconPath": "./static/page/footer_icon2_2.png",
				"text": "分类"
			},
			{
				"pagePath": "pages/home/<USER>/shopcard",
				"iconPath": "./static/page/footer_icon3_1.png",
				"selectedIconPath": "./static/page/footer_icon3_2.png",
				"text": "购物车"
			},
			{
				"pagePath": "pages/home/<USER>/notice",
				"iconPath": "./static/page/footer_icon5_1.png",
				"selectedIconPath": "./static/page/footer_icon5_2.png",
				"text": "消息"
			},
			{
				"pagePath": "pages/home/<USER>/userinfo",
				"iconPath": "./static/page/footer_icon4_1.png",
				"selectedIconPath": "./static/page/footer_icon4_2.png",
				"text": "我的"
			}
		]
	}
	// ,"condition": { //模式配置，仅开发期间生效
	// 		"current": 0, //当前激活的模式（list 的索引项）
	// 		"list": [{
	// 			"name": "test", //模式名称
	// 			"path": "pages/sub/text/text" //启动页面，必选
	// 		}]
	// 	}
}